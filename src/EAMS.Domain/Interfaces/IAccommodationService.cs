using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;

namespace EAMS.Domain.Interfaces;

public interface IAccommodationService
{
    Task<IEnumerable<Accommodation>> GetAll(
        string? search = null,
        AccommodationType? accommodationType = null,
        Density? density = null,
        Duration? duration = null,
        Region? region = null,
        bool? inactive = null);
    Task<Accommodation?> GetById(Int64 id);
    Task<Accommodation> Create(Accommodation accommodation);
    Task<Accommodation> Update(Accommodation accommodation);
    Task<bool> Delete(Int64 id);
}
