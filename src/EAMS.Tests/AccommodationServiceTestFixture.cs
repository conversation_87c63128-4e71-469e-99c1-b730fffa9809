using System.Linq.Expressions;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Services;
using Moq;
using Xunit;

namespace EAMS.Tests;

public class AccommodationServiceTestFixture
{
    private Mock<IAccommodationRepository> _mockAccommodationRepository;
    private AccommodationService _targetService;
    private List<Accommodation> _testAccommodations;

    public AccommodationServiceTestFixture()
    {
        _mockAccommodationRepository = new Mock<IAccommodationRepository>();
        _targetService = new AccommodationService(_mockAccommodationRepository.Object);

        // Setup test data
        _testAccommodations = new List<Accommodation>
        {
            new Accommodation
            {
                Id = 1,
                Name = "Grand Hotel Melbourne",
                StreetLine1 = "123 Collins Street",
                StreetLine2 = null,
                Suburb = "Melbourne",
                State = "VIC",
                Postcode = "3000",
                Region = Region.MelbourneCBD,
                AccommodationType = AccommodationType.Hotel,
                Density = Density.High,
                Duration = new List<Duration> { Duration.ShortTermStay }
            },
            new Accommodation
            {
                Id = 2,
                Name = "Cozy Motel",
                StreetLine1 = "456 Main Street",
                StreetLine2 = "Unit 5",
                Suburb = "Richmond",
                State = "VIC",
                Postcode = "3121",
                Region = Region.InnerEasternMelbourne,
                AccommodationType = AccommodationType.MotelOrMotorInn,
                Density = Density.Medium,
                Duration = new List<Duration> { Duration.LongTermStay }
            },
            new Accommodation
            {
                Id = 3,
                Name = "Seaside Apartments",
                StreetLine1 = "789 Beach Road",
                StreetLine2 = null,
                Suburb = "St Kilda",
                State = "VIC",
                Postcode = "3182",
                Region = Region.BaysidePeninsula,
                AccommodationType = AccommodationType.ServicedApartment,
                Density = Density.Low,
                Duration = new List<Duration> { Duration.SingleTermStay, Duration.ShortTermStay }
            }
        };
    }

    [Fact]
    public async Task GetAll_WithNoFilters_ShouldReturnAllAccommodations()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(null))
            .ReturnsAsync(_testAccommodations);

        // Act
        var result = await _targetService.GetAll();

        // Assert
        Assert.Equal(3, result.Count());
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(null), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithEmptySearchTerm_ShouldReturnAllAccommodations()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(null))
            .ReturnsAsync(_testAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "");

        // Assert
        Assert.Equal(3, result.Count());
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(null), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchByName_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Name.ToLower().Contains("hotel")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "hotel");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Name == "Grand Hotel Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchByStreetAddress_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.StreetLine1.ToLower().Contains("main")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "main");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.StreetLine1 == "456 Main Street");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchBySuburb_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Suburb.ToLower().Contains("melbourne")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "melbourne");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Suburb == "Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchByPostcode_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Postcode.ToLower().Contains("3000")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "3000");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Postcode == "3000");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchCaseInsensitive_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Name.ToLower().Contains("hotel")).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(search: "HOTEL");

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Name == "Grand Hotel Melbourne");
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchNoMatches_ShouldReturnEmptyList()
    {
        // Arrange
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(new List<Accommodation>());

        // Act
        var result = await _targetService.GetAll(search: "nonexistent");

        // Assert
        Assert.Empty(result);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithAccommodationTypeFilter_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.AccommodationType == AccommodationType.Hotel).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(accommodationType: AccommodationType.Hotel);

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.AccommodationType == AccommodationType.Hotel);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithDensityFilter_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Density == Density.High).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(density: Density.High);

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Density == Density.High);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithDurationFilter_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Duration.Contains(Duration.ShortTermStay)).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(duration: Duration.ShortTermStay);

        // Assert
        Assert.Equal(2, result.Count()); // Grand Hotel and Seaside Apartments both support ShortTermStay
        Assert.All(result, a => Assert.Contains(Duration.ShortTermStay, a.Duration));
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithRegionFilter_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Region == Region.MelbourneCBD).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(region: Region.MelbourneCBD);

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Region == Region.MelbourneCBD);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithInactiveFilter_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations.Where(a => a.Inactive == false).ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(inactive: false);

        // Assert
        Assert.Equal(3, result.Count()); // All test accommodations are active
        Assert.All(result, a => Assert.False(a.Inactive));
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithMultipleFilters_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations
            .Where(a => a.AccommodationType == AccommodationType.Hotel && a.Region == Region.MelbourneCBD)
            .ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(
            accommodationType: AccommodationType.Hotel,
            region: Region.MelbourneCBD);

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.AccommodationType == AccommodationType.Hotel && a.Region == Region.MelbourneCBD);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }

    [Fact]
    public async Task GetAll_WithSearchAndFilters_ShouldReturnMatchingAccommodations()
    {
        // Arrange
        var expectedAccommodations = _testAccommodations
            .Where(a => a.Name.ToLower().Contains("hotel") && a.AccommodationType == AccommodationType.Hotel)
            .ToList();
        _mockAccommodationRepository
            .Setup(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()))
            .ReturnsAsync(expectedAccommodations);

        // Act
        var result = await _targetService.GetAll(
            search: "hotel",
            accommodationType: AccommodationType.Hotel);

        // Assert
        Assert.Single(result);
        Assert.Contains(result, a => a.Name.Contains("Hotel") && a.AccommodationType == AccommodationType.Hotel);
        _mockAccommodationRepository.Verify(x => x.GetAllAsync(It.IsAny<Expression<Func<Accommodation, bool>>>()), Times.Once);
    }
}
