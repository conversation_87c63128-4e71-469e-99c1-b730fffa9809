namespace EAMS.API.DTOs;

/// <summary>
/// Generic response wrapper for list endpoints with permissions
/// </summary>
/// <typeparam name="T">The type of the list items</typeparam>
public class ListResponseDto<T>
{
    /// <summary>
    /// The list of items
    /// </summary>
    public IEnumerable<T> Data { get; set; } = new List<T>();

    /// <summary>
    /// Permissions relevant to this list
    /// </summary>
    public Dictionary<string, bool> Permissions { get; set; } = new();
}
