namespace EAMS.API.DTOs;

/// <summary>
/// Generic response wrapper for form data
/// </summary>
/// <typeparam name="T">The type of the model data</typeparam>
public class FormResponseDto<T>
{
    /// <summary>
    /// Form data including model and metadata
    /// </summary>
    public FormDataDto<T> Data { get; set; } = new();

    /// <summary>
    /// User permissions for the form
    /// </summary>
    public Dictionary<string, bool> Permissions { get; set; } = new();
}

/// <summary>
/// Container for form data including model and metadata
/// </summary>
/// <typeparam name="T">The type of the model data</typeparam>
public class FormDataDto<T>
{
    /// <summary>
    /// The model data for the form
    /// </summary>
    public T? Model { get; set; }

    /// <summary>
    /// Form metadata including enum options
    /// </summary>
    public FormMetadataDto FormMetadata { get; set; } = new();
}

/// <summary>
/// Form metadata containing enum options
/// </summary>
public class FormMetadataDto
{
    /// <summary>
    /// Accommodation type options
    /// </summary>
    public List<EnumOptionDto> AccommodationType { get; set; } = new();

    /// <summary>
    /// Density options
    /// </summary>
    public List<EnumOptionDto> Density { get; set; } = new();

    /// <summary>
    /// Region options
    /// </summary>
    public List<EnumOptionDto> Region { get; set; } = new();

    /// <summary>
    /// Duration options
    /// </summary>
    public List<EnumOptionDto> Duration { get; set; } = new();

    /// <summary>
    /// Amenity type options
    /// </summary>
    public List<EnumOptionDto> AmenityType { get; set; } = new();
}



/// <summary>
/// Represents an enum option with value and display name
/// </summary>
public class EnumOptionDto
{
    /// <summary>
    /// The enum value (numeric)
    /// </summary>
    public int Value { get; set; }

    /// <summary>
    /// The display name for the enum value
    /// </summary>
    public string Name { get; set; } = string.Empty;
}
