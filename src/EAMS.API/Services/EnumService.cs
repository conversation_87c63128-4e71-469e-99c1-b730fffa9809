using EAMS.API.DTOs;
using EAMS.Domain.Entities.Enums;

namespace EAMS.API.Services;

/// <summary>
/// Service for converting enums to DTOs for form metadata
/// </summary>
public class EnumService
{
    /// <summary>
    /// Get all accommodation type options
    /// </summary>
    public List<EnumOptionDto> GetAccommodationTypeOptions()
    {
        return Enum.GetValues<AccommodationType>()
            .Select(e => new EnumOptionDto
            {
                Value = (int)e,
                Name = e.ToString()
            })
            .ToList();
    }

    /// <summary>
    /// Get all density options
    /// </summary>
    public List<EnumOptionDto> GetDensityOptions()
    {
        return Enum.GetValues<Density>()
            .Select(e => new EnumOptionDto
            {
                Value = (int)e,
                Name = e.ToString()
            })
            .ToList();
    }

    /// <summary>
    /// Get all region options
    /// </summary>
    public List<EnumOptionDto> GetRegionOptions()
    {
        return Enum.GetValues<Region>()
            .Select(e => new EnumOptionDto
            {
                Value = (int)e,
                Name = e.ToString()
            })
            .ToList();
    }

    /// <summary>
    /// Get all duration options
    /// </summary>
    public List<EnumOptionDto> GetDurationOptions()
    {
        return Enum.GetValues<Duration>()
            .Select(e => new EnumOptionDto
            {
                Value = (int)e,
                Name = e.ToString()
            })
            .ToList();
    }

    /// <summary>
    /// Get all amenity type options
    /// </summary>
    public List<EnumOptionDto> GetAmenityTypeOptions()
    {
        return Enum.GetValues<AmenityType>()
            .Select(e => new EnumOptionDto
            {
                Value = (int)e,
                Name = e.ToString()
            })
            .ToList();
    }

    /// <summary>
    /// Get complete form metadata with all enum options
    /// </summary>
    public FormMetadataDto GetFormMetadata()
    {
        return new FormMetadataDto
        {
            AccommodationType = GetAccommodationTypeOptions(),
            Density = GetDensityOptions(),
            Region = GetRegionOptions(),
            Duration = GetDurationOptions(),
            AmenityType = GetAmenityTypeOptions()
        };
    }
}
