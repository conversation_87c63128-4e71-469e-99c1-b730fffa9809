using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.API.Services;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using AutoMapper;
using System.Linq.Expressions;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Organisations")]
[Route("api/[controller]")]
public class OrganisationsController : ControllerBase
{
    private readonly IOrganisationService _organisationService;
    private readonly IUserService _userService;
    private readonly ILogger<OrganisationsController> _logger;
    private readonly IMapper _mapper;
    private readonly EnumService _enumService;

    public OrganisationsController(
        IOrganisationService organisationService,
        IUserService userService,
        ILogger<OrganisationsController> logger,
        IMapper mapper,
        EnumService enumService)
    {
        _organisationService = organisationService;
        _userService = userService;
        _logger = logger;
        _mapper = mapper;
        _enumService = enumService;
    }

    /// <summary>
    /// Get all organisations
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<ListResponseDto<OrganisationDto>>> GetOrganisations()
    {
        var organisations = await _organisationService.GetAll();
        var mappedOrganisations = _mapper.Map<IEnumerable<OrganisationDto>>(organisations);

        var response = new ListResponseDto<OrganisationDto>
        {
            Data = mappedOrganisations,
            Permissions = new Dictionary<string, bool>
            {
                ["canCreateOrganisation"] = User.IsInRole("Managers")
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Get organisation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<ListResponseDto<OrganisationDto>>> GetOrganisation(Guid id)
    {
        var organisation = await _organisationService.GetById(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        var mappedOrganisation = _mapper.Map<OrganisationDto>(organisation);
        var response = new ListResponseDto<OrganisationDto>
        {
            Data = new List<OrganisationDto> { mappedOrganisation },
            Permissions = new Dictionary<string, bool>
            {
                ["canCreateOrganisation"] = User.IsInRole("Managers")
            }
        };
        return Ok(response);
    }

    /// <summary>
    /// Get form data for creating a new organisation
    /// </summary>
    [HttpGet("new")]
    [Authorize(Roles = "Administrators, Managers")]
    public ActionResult<FormResponseDto<OrganisationDto>> GetNewOrganisationForm()
    {
        var formMetadata = _enumService.GetFormMetadata();
        var permissions = new Dictionary<string, bool>
        {
            ["canCreateOrganisation"] = User.IsInRole("Managers")
        };

        var response = new FormResponseDto<OrganisationDto>
        {
            Data = new FormDataDto<OrganisationDto>
            {
                Model = new OrganisationDto(),
                FormMetadata = formMetadata
            },
            Permissions = permissions
        };

        return Ok(response);
    }

    /// <summary>
    /// Get form data for editing an existing organisation
    /// </summary>
    [HttpGet("{id}/edit")]
    [Authorize(Roles = "Users")]
    public async Task<ActionResult<FormResponseDto<OrganisationDto>>> GetEditOrganisationForm(Guid id)
    {
        var organisation = await _organisationService.GetById(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        var organisationDto = _mapper.Map<OrganisationDto>(organisation);
        var formMetadata = _enumService.GetFormMetadata();
        var isManager = User.IsInRole("Managers");

        var permissions = new Dictionary<string, bool>
        {
            ["canEditOrganisation"] = isManager,
            ["canDeleteOrganisation"] = isManager
        };

        var response = new FormResponseDto<OrganisationDto>
        {
            Data = new FormDataDto<OrganisationDto>
            {
                Model = organisationDto,
                FormMetadata = formMetadata
            },
            Permissions = permissions
        };

        return Ok(response);
    }

    /// <summary>
    /// Create a new organisation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<OrganisationDto>> CreateOrganisation(OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var createdOrganisation = await _organisationService.Create(organisation);
        var response = _mapper.Map<OrganisationDto>(createdOrganisation);

        return CreatedAtAction(nameof(GetOrganisation), new { id = createdOrganisation.Id }, response);
    }

    /// <summary>
    /// Update an existing organisation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<OrganisationDto>> UpdateOrganisation(Guid id, OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var updatedOrganisation = await _organisationService.Update(organisation);
        var response = _mapper.Map<OrganisationDto>(updatedOrganisation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an organisation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<bool>> DeleteOrganisation(Guid id)
    {
        var result = await _organisationService.Delete(id);
        return Ok(result);
    }

    [HttpGet("{id}/users")]
    [Authorize(Roles = "Administrators, Managers")]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetOrganisationUsers(Guid id)
    {
        // check if organisation exists
        var organisation = _organisationService.GetById(id);
        if (organisation is null)
            return NotFound($"Could not find organisation with Id: {id}");

        var userContext = HttpContext.User;
        if (userContext.HasClaim(c => c.Type == "roles"))
        {
            var roles = userContext.Claims.Where(c => c.Type == "roles")
                .Select(c => c.Value)
                .ToList();
            if (!roles.Contains("Administrators"))
            {
                if (await _organisationService.CanManageOrganisationAsync(id) == false)
                    return Unauthorized();

            }

            var orgUsers = await _organisationService.GetOrganisationUsers(id);

            return Ok(_mapper.Map<IEnumerable<UserDto>>(orgUsers));
        }

        return Unauthorized();
    }
}
